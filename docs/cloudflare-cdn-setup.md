# Cloudflare CDN 配置指南

## 问题描述

当使用 Cloudflare CDN 时，可能会出现用户登录到别人账号的问题。这通常是由于 CDN 缓存了包含用户会话信息的响应导致的。

## 解决方案

### 1. 应用程序配置

在 `config.yaml` 中添加以下配置：

```yaml
# 启用 HTTPS 模式（如果使用 Cloudflare）
https: true

# 设置 Cloudflare 可信头部
trusted_header: "CF-Connecting-IP"

# 确保会话密钥已设置
session_secret: "your-secret-key-here"
```

### 2. Cloudflare 页面规则配置

在 Cloudflare 控制台中，为以下路径设置页面规则：

#### 规则 1: 绕过认证相关接口的缓存
- **URL 模式**: `yourdomain.com/api/user/*`
- **设置**:
  - Cache Level: Bypass
  - Browser Cache TTL: Respect Existing Headers

#### 规则 2: 绕过 OAuth 相关接口的缓存
- **URL 模式**: `yourdomain.com/api/oauth/*`
- **设置**:
  - Cache Level: Bypass
  - Browser Cache TTL: Respect Existing Headers

#### 规则 3: 绕过所有 API 接口的缓存（推荐）
- **URL 模式**: `yourdomain.com/api/*`
- **设置**:
  - Cache Level: Bypass
  - Browser Cache TTL: Respect Existing Headers

### 3. Cloudflare 缓存规则（新版界面）

如果使用新版 Cloudflare 界面，可以在 "缓存" -> "缓存规则" 中设置：

#### 规则名称: Bypass API Cache
- **字段**: URI Path
- **运算符**: starts with
- **值**: `/api/`
- **操作**: Bypass cache

### 4. 验证配置

部署后，可以通过以下方式验证配置是否生效：

1. 检查响应头部：
   ```bash
   curl -I https://yourdomain.com/api/user/self
   ```
   应该看到：
   - `CF-Cache-Status: BYPASS`
   - `Cache-Control: no-cache, no-store, must-revalidate, private`

2. 测试登录功能：
   - 在不同浏览器/无痕窗口中登录不同账号
   - 确认不会出现账号混乱

### 5. 额外的安全建议

#### 5.1 启用 Cloudflare 的安全功能
- 启用 "Under Attack Mode"（如果需要）
- 配置 WAF 规则
- 启用 Bot Fight Mode

#### 5.2 监控和日志
- 在 Cloudflare Analytics 中监控缓存命中率
- 检查 Security Events 是否有异常

#### 5.3 备用方案
如果问题仍然存在，可以考虑：
- 使用子域名专门处理 API 请求（如 `api.yourdomain.com`）
- 在 API 路径前添加随机参数或时间戳
- 使用 JWT token 替代 session cookie

## 常见问题

### Q: 为什么无痕窗口可以正常登录？
A: 无痕窗口不会使用缓存的响应，因此避免了会话混乱问题。

### Q: 如何确认是 CDN 缓存问题？
A: 检查响应头中的 `CF-Cache-Status`，如果是 `HIT` 则说明响应被缓存了。

### Q: 页面规则的优先级如何设置？
A: 更具体的规则应该有更高的优先级。建议将 `/api/user/*` 的优先级设置为最高。

## 技术原理

本次修复主要包含以下改进：

1. **Session 配置优化**：
   - 将 `SameSite` 从 `Strict` 改为 `Lax`，提高 CDN 兼容性
   - 根据 HTTPS 配置动态设置 `Secure` 属性

2. **防缓存头部增强**：
   - 添加 `private` 指令确保 CDN 不缓存
   - 增加 `Vary` 头部处理不同的请求
   - 添加 Cloudflare 特定的 `CF-Cache-Status: BYPASS`

3. **中间件改进**：
   - 新增 `SessionSecurity` 中间件专门处理会话安全
   - 新增 `CloudflareHeaders` 中间件处理 CDN 特定配置

4. **路由级别的防护**：
   - 为所有认证相关的路由添加防缓存中间件
   - 确保用户数据接口不被缓存
