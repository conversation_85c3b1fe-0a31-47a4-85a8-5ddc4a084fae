package middleware

import (
	"github.com/gin-gonic/gin"
)

// NoCache 强制不缓存
func NoCache() func(c *gin.Context) {
	return func(c *gin.Context) {
		c.<PERSON>er("Cache-Control", "no-cache, no-store, must-revalidate, private")
		c.<PERSON><PERSON>("Pragma", "no-cache")
		c.<PERSON>("Expires", "0")
		c.<PERSON>("Vary", "<PERSON><PERSON>, Authorization")
		c.Next()
	}
}

// NoSessionCache 针对会话相关的接口，添加更强的防缓存策略
func NoSessionCache() func(c *gin.Context) {
	return func(c *gin.Context) {
		c.<PERSON><PERSON>("Cache-Control", "no-cache, no-store, must-revalidate, private, max-age=0")
		c.<PERSON><PERSON>("Pragma", "no-cache")
		c.<PERSON><PERSON>("Expires", "0")
		c.<PERSON>("Vary", "<PERSON><PERSON>, Authorization, X-Requested-With")
		// Cloudflare 特定的头部
		c.<PERSON><PERSON>("CF-Cache-Status", "BYPASS")
		c.Next()
	}
}
