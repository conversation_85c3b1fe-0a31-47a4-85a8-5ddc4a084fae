package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

// CloudflareHeaders 添加 Cloudflare 特定的头部处理
func CloudflareHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否使用了 Cloudflare
		trustedHeader := viper.GetString("trusted_header")
		if trustedHeader == "CF-Connecting-IP" {
			// 添加 Cloudflare 特定的头部 (CF-Cache-Status 是只读的，不能设置)
			c.<PERSON>er("CF-Cache-Control", "no-cache")
		}
		
		// 添加通用的防缓存头部
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-XSS-Protection", "1; mode=block")
		
		c.Next()
	}
}

// SessionSecurity 为会话相关的接口添加额外的安全头部
func SessionSecurity() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加会话安全相关的头部
		c.<PERSON><PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")
		c.<PERSON><PERSON>("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
		
		// 确保响应不被缓存
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate, private, max-age=0")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		c.Header("Vary", "Cookie, Authorization, X-Requested-With, User-Agent")
		
		// Cloudflare 特定
		trustedHeader := viper.GetString("trusted_header")
		if trustedHeader == "CF-Connecting-IP" {
			c.Header("CF-Cache-Control", "no-cache, no-store")
		}
		
		c.Next()
	}
}
