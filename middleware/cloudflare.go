package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

// SecurityHeaders 添加通用的安全头部
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加通用的安全头部
		c.<PERSON><PERSON>("X-Frame-Options", "DENY")
		c.<PERSON><PERSON>("X-Content-Type-Options", "nosniff")
		c.<PERSON><PERSON>("X-XSS-Protection", "1; mode=block")

		c.Next()
	}
}

// SessionSecurity 为会话相关的接口添加额外的安全头部
func SessionSecurity() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加会话安全相关的头部
		c.<PERSON><PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")
		c.<PERSON><PERSON>("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
		
		// 确保响应不被缓存 - 使用更强的防缓存策略
		c.<PERSON><PERSON>("Cache-Control", "no-cache, no-store, must-revalidate, private, max-age=0")
		c.<PERSON><PERSON>("<PERSON>ragma", "no-cache")
		c.<PERSON><PERSON>("Expires", "0")
		c.<PERSON><PERSON>("Vary", "<PERSON>ie, Authorization, X-Requested-With, User-Agent")
		
		c.Next()
	}
}
